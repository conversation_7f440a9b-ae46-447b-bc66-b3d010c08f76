<?php

declare(strict_types=1);

namespace App\Models\UserDb\Owners;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Documents\Contracts\ContractChargedRenta;
use App\Models\UserDb\Documents\Contracts\ContractContragent;
use App\Models\UserDb\Documents\Contracts\ContractOwner;
use App\Models\UserDb\Documents\Contracts\ContractPlot;
use App\Models\UserDb\Documents\Contracts\PlotOwner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Owner extends Model
{
    protected $table = 'su_owners';

    protected $fillable = [
        'name',
        'surname',
        'lastname',
        'egn',
        'lk_nomer',
        'lk_izdavane',
        'company_name',
        'eik',
        'phone',
        'fax',
        'mobile',
        'email',
        'address',
        'owner_type',
        'mol',
        'company_address',
        'is_dead',
        'iban',
        'rent_place',
        'is_foreigner',
        'remark',
        'prepiska',
        'bic',
        'bank_name',
        'dead_date',
        'post_payment_fields',
        'country',
        'birthday',
    ];

    protected $casts = [
        'lk_izdavane' => 'date',
        'is_dead' => 'boolean',
        'is_foreigner' => 'boolean',
        'dead_date' => 'date',
        'birthday' => 'date',
        'post_payment_fields' => 'array',
    ];

    /**
     * Get the contract owners for this owner
     */
    public function contractOwners(): HasMany
    {
        return $this->hasMany(ContractOwner::class, 'owner_id');
    }

    /**
     * Get the contract contragents for this owner
     */
    public function contractContragents(): HasMany
    {
        return $this->hasMany(ContractContragent::class, 'owner_id');
    }

    /**
     * Get the charged rentas for this owner
     */
    public function chargedRentas(): HasMany
    {
        return $this->hasMany(ContractChargedRenta::class, 'owner_id');
    }

    /**
     * Get the plot owners for this owner
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'owner_id');
    }

    /**
     * Get all contract plots for this owner through plot owners
     */
    public function contractPlots(): HasManyThrough
    {
        return $this->hasManyThrough(ContractPlot::class, PlotOwner::class, 'owner_id', 'id', 'id', 'pc_rel_id');
    }

    /**
     * Get all contracts for this owner through plot owners and contract plots
     */
    public function contracts(): HasManyThrough
    {
        return $this->hasManyThrough(Contract::class, PlotOwner::class, 'owner_id', 'id', 'id', 'contract_id')
                    ->join('su_contracts_plots_rel', 'su_contracts_plots_rel.id', '=', 'su_plots_owners_rel.pc_rel_id');
    }

    /**
     * Scope for active owners (not dead)
     */
    public function scopeActive($query)
    {
        return $query->where('is_dead', false);
    }

    /**
     * Scope for dead owners
     */
    public function scopeDeceased($query)
    {
        return $query->where('is_dead', true);
    }

    /**
     * Scope for individual owners (not companies)
     */
    public function scopeIndividuals($query)
    {
        return $query->whereNull('company_name')->orWhere('company_name', '');
    }

    /**
     * Scope for company owners
     */
    public function scopeCompanies($query)
    {
        return $query->whereNotNull('company_name')->where('company_name', '!=', '');
    }

    /**
     * Get full name for individual owners
     */
    public function getFullNameAttribute(): string
    {
        if (!empty($this->company_name)) {
            return $this->company_name;
        }

        $parts = array_filter([
            $this->name,
            $this->surname,
            $this->lastname,
        ]);

        return implode(' ', $parts);
    }

    /**
     * Get display name (company name or full name)
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->full_name;
    }
}
