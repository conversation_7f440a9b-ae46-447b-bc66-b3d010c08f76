<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

class OwnerNameEgnFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = '
            TRIM(su_owners.name)
            || \' \' || TRIM(su_owners.surname) 
            || \' \' || TRIM(su_owners.lastname)
            || CASE WHEN su_owners.egn NOTNULL AND TRIM(su_owners.egn) <> \'\' 
                THEN \' (\' || TRIM(su_owners.egn) || \')\' 
                ELSE \'\' 
            END
        ';

    }
}
