<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $sql = "CREATE OR REPLACE FUNCTION get_contracts_for_renew(p_year INTEGER, p_is_sublease BOOLEAN)
                RETURNS TABLE (
                    cp_rel_id INTEGER,
                    c_num VARCHAR,
                    curr_contract_area NUMERIC,
                    curr_total_contract_area NUMERIC,
                    next_total_contract_area NUMERIC,
                    next_cp_rel_ids JSONB,
                    can_renew BOOLEAN,
                    has_decreased_area_next_year BOOLEAN
                ) AS \$\$
                BEGIN
                    RETURN QUERY
                        WITH next_year_plot_total_contract_area AS (
                            SELECT
                                next.plot_id,
                                next.document_area,
                                ROUND(SUM(next.contract_area)::NUMERIC, 3) AS total_contract_area,
                                JSONB_AGG(next.cp_rel_id) AS cp_rel_ids
                            FROM 
                                get_plots_in_contracts_for_year(p_year + 1, p_is_sublease) AS next
                            GROUP BY
                                next.plot_id,
                                next.document_area
                        ),
                        current_year_plot_total_contract_area AS (
                            SELECT
                                curr.plot_id,
                                curr.document_area,
                                ROUND(SUM(curr.contract_area)::NUMERIC, 3) AS total_contract_area
                            FROM 
                                get_plots_in_contracts_for_year(p_year, p_is_sublease) AS curr
                            GROUP BY
                                curr.plot_id,
                                curr.document_area
                        )
                        SELECT
                            curr.cp_rel_id,
                            curr.c_num,
                            curr.contract_area AS curr_contract_area,
                            curr_total.total_contract_area AS curr_total_contract_area,
                            next_total.total_contract_area AS next_total_contract_area,
                            next_total.cp_rel_ids as next_cp_rel_ids,
                            (
                                ROUND((COALESCE(next_total.total_contract_area, 0) + curr.contract_area)::NUMERIC, 3) <= ROUND(curr_total.document_area::NUMERIC, 3)
                                AND 
                                (curr.contract_start_date = ((curr.year - 1) || '-10-01')::DATE AND curr.contract_due_date = ((curr.year || '-09-30')::DATE))
                            ) AS can_renew,
                            COALESCE(next_total.total_contract_area, 0) < curr_total.total_contract_area AS has_decreased_area_next_year
                        FROM
                            get_plots_in_contracts_for_year(p_year, p_is_sublease) AS curr
                        JOIN current_year_plot_total_contract_area AS curr_total
                            ON curr_total.plot_id = curr.plot_id
                        LEFT JOIN next_year_plot_total_contract_area AS next_total
                            ON next_total.plot_id = curr.plot_id;
                END;
                $$ LANGUAGE plpgsql;
        ";

        DB::unprepared($sql);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP FUNCTION get_contracts_for_renew(p_year integer, p_is_sublease BOOLEAN)');
    }
};
