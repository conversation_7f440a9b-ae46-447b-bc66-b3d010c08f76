<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContragentFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->includeRepresentatives = true;
    }

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(
            fn ($subQ) => $subQ->whereIn(
                DB::raw("
                    CASE WHEN su_owners.company_name NOTNULL AND TRIM(su_owners.company_name) <> ''
                        THEN 
                            TRIM(su_owners.company_name) 
                            || CASE WHEN su_owners.eik NOTNULL AND TRIM(su_owners.eik) <> ''  THEN ' (' || TRIM (su_owners.eik) || ')' ELSE '' END
                        ELSE 
                            TRIM(su_owners.name) || ' ' || TRIM(su_owners.surname) || ' ' || TRIM(su_owners.lastname) 
                            || CASE WHEN su_owners.egn NOTNULL AND TRIM(su_owners.egn) <> '' THEN ' (' || TRIM (su_owners.egn) || ')' ELSE '' END
                    END
                "),
                $this->filterValues
            )
                ->orWhereIn(
                    DB::raw("
                    TRIM(su_owners_reps.rep_name) || ' ' || TRIM(su_owners_reps.rep_surname) || ' ' || TRIM(su_owners_reps.rep_lastname) || ' (' || TRIM (su_owners_reps.rep_egn) || ')'
                "),
                    $this->filterValues
                )
        );

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $valueExpression = " 
            UNNEST(
                    ARRAY_REMOVE(
                        ARRAY[
                            CASE WHEN su_owners.company_name NOTNULL AND TRIM(su_owners.company_name) <> ''
                                THEN 
                                    TRIM(su_owners.company_name) 
                                    || CASE WHEN su_owners.eik NOTNULL AND TRIM(su_owners.eik) <> ''  THEN ' (' || TRIM (su_owners.eik) || ')' ELSE '' END
                                ELSE 
                                    TRIM(su_owners.name) || ' ' || TRIM(su_owners.surname) || ' ' || TRIM(su_owners.lastname) 
                                    || CASE WHEN su_owners.egn NOTNULL AND TRIM(su_owners.egn) <> '' THEN ' (' || TRIM (su_owners.egn) || ')' ELSE '' END
                            END,
                            TRIM(su_owners_reps.rep_name) || ' ' || TRIM(su_owners_reps.rep_surname) || ' ' || TRIM(su_owners_reps.rep_lastname) || ' (' || TRIM (su_owners_reps.rep_egn) || ')'
                        ],
                        NULL
                    )
                )
        ";

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }
}
