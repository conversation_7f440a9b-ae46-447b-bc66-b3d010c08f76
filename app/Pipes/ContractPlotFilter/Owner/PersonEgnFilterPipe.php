<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class PersonEgnFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = "
            UNNEST(
                ARRAY_REMOVE(
                    ARRAY[
                        CASE WHEN su_owners.egn IS NOT NULL AND su_owners.egn <> '' THEN su_owners.egn ELSE NULL END,
                        CASE WHEN su_owners_reps.rep_egn IS NOT NULL AND su_owners_reps.rep_egn <> '' THEN su_owners_reps.rep_egn ELSE NULL END
                    ],
                    NULL
                )
            )
        ";
        $this->includeRepresentatives = true;
    }

    public function handle(Builder $query, Closure $next)
    {

        $className = self::class;
        if (! is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->whereIn(DB::raw($this->filterColumn), $this->filterValues);

        return $next($query);
    }
}
