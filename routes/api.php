<?php

declare(strict_types=1);

use App\Http\Controllers\Documents\Contracts\ContractsController;
use App\Http\Controllers\Documents\Contracts\ContractsRenewController;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('/documents')->group(function () {
    Route::prefix('/{type}')
        ->whereIn('type', DocumentTypeEnum::values()->toArray())
        ->group(function () {
            Route::controller(ContractsController::class)
                ->group(function () {
                    Route::get('/', 'index');
                    Route::get('/filter-items', 'getFilterItems');
                    Route::get('/{id}', 'show')->where('id', '[0-9]+');
                });
        });

    // Renew endpoints with restricted type parameter (only 'contracts' and 'subleases')
    Route::prefix('/{type}')
        ->whereIn('type', [DocumentTypeEnum::Contracts->value, DocumentTypeEnum::Subleases->value])
        ->group(function () {
            Route::prefix('/renew')
                ->controller(ContractsRenewController::class)
                ->group(function () {
                    Route::get('/', 'index');
                    Route::get('/filter-items', 'getFilterItems');
                });
        });
});

// Contract renewal routes
Route::prefix('/documents')->group(function () {
    Route::controller(ContractsRenewController::class)->group(function () {
        Route::post('/renew-contracts', 'renewContracts');
    });
});
