<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\ContractRenew;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class BaseContractRenewFilterPipe implements ContractPlotFilterPipe
{
    protected string $filterColumn;
    protected array $filterValue;

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValue)) {
            return $next($query);
        }


        [$startFarmingYear, $endFarmingYear] = explode('/', $this->filterValue['year']);
        $farmingYear = (int) $endFarmingYear;
        $isSublease = $this->filterValue['is_sublease'];
        $this->joinRequiredTables($query, $farmingYear, $isSublease);

        if (! is_string($this->filterValue['year']) || ! is_bool($this->filterValue['value']) || ! is_bool($this->filterValue['is_sublease'])) {
            return $next($query);
        }

        if (! is_null($this->filterValue['value'])) {
            $filterValueStr = $this->filterValue['value'] ? 'true' : 'false';
            $query->whereRaw("{$this->filterColumn} = {$filterValueStr}");
        }

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        throw new Exception("The {$className} does not support filter items selection");

    }

    public function joinRequiredTables(Builder $query, ?int $farmingYear = null, ?bool $isSublease = null): void
    {
        $joinedTables = collect($query->joins)->pluck('table');
        $className = self::class;
        $isSubleaseStr = $isSublease ? 'true' : 'false';

        $contractsForRenewSub = DB::table(
            DB::raw("get_contracts_for_renew({$farmingYear}, {$isSubleaseStr}) AS cfr(
                cp_rel_id, c_num, curr_contract_area, curr_total_contract_area, next_total_contract_area, next_cp_rel_ids, can_renew, has_decreased_area_next_year
            )")
        )->select(['*']);

        match($query->from) {
            'su_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_subleases_plots_contracts_rel') && $isSublease,
                    fn () => $query->leftJoin('su_subleases_plots_contracts_rel', 'su_subleases_plots_contracts_rel.sublease_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin(
                        'su_contracts_plots_rel',
                        fn ($join) => $join
                            ->on('su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                            ->when(
                                $isSublease,
                                fn () => $join->orOn('su_contracts_plots_rel.id', '=', 'su_subleases_plots_contracts_rel.pc_rel_id')
                            )
                    )
                )
                ->when(
                    ! $joinedTables->contains('contracts_for_renew'),
                    fn () => $query->joinSub($contractsForRenewSub, 'contracts_for_renew', 'contracts_for_renew.cp_rel_id', '=', 'su_contracts_plots_rel.id')
                ),
            default => throw new Exception("Invalid base table for filtering by {$className}")
        };
    }
}
