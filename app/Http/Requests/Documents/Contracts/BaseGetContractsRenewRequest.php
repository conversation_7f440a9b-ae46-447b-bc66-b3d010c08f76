<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;
use DateTime;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BaseGetContractsRenewRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'filter_params' => ['required', 'array'],

            // Contract types validation
            'filter_params.contract_types' => ['array'],
            'filter_params.contract_types.*' => ['nullable', Rule::in(ContractTypeEnum::values())],

            // Plot filters validation
            'filter_params.kad_ident' => ['array'],
            'filter_params.kad_ident.*' => ['string', 'nullable'],

            'filter_params.virtual_ekatte_name' => ['array'],
            'filter_params.virtual_ekatte_name.*' => ['string', 'nullable'],

            // Contract filters validation
            'filter_params.cnum' => ['array'],
            'filter_params.cnum.*' => ['string', 'nullable'],

            'filter_params.virtual_contract_type' => ['array'],
            'filter_params.virtual_contract_type.*' => ['string', 'nullable'],

            'filter_params.farming_name' => ['array'],
            'filter_params.farming_name.*' => ['string', 'nullable'],

            'filter_params.from_sublease' => ['array'],
            'filter_params.from_sublease.*' => ['integer', 'nullable'],

            // Owner filters validation
            'filter_params.contragent' => ['array'],
            'filter_params.contragent.*' => ['string', 'nullable'],

            'filter_params.tenant' => ['array'],
            'filter_params.tenant.*' => ['string', 'nullable'],

            'filter_params.buyer' => ['array'],
            'filter_params.buyer.*' => ['string', 'nullable'],

            'filter_params.creditor' => ['array'],
            'filter_params.creditor.*' => ['string', 'nullable'],

            'filter_params.has_decreased_area_next_year' => ['boolean', 'nullable'],
            'filter_params.can_renew' => ['boolean', 'nullable'],

            // Pagination validation
            'page' => ['integer', 'nullable'],
            'limit' => ['integer', 'nullable'],

            // Sort validation
            'sort' => ['array', 'nullable'],
            'sort.*.column' => ['string'],
            'sort.*.direction' => ['string', 'in:asc,desc'],

        ];
    }

    public function validated($key = null, $default = null)
    {
        $filterParams = parent::validated()['filter_params'];

        return $this->getGroupedFilterParams($filterParams);

    }

    protected function prepareForValidation()
    {
        // Decode the filter_params JSON string into an associative array
        $filterParamsDecoded = json_decode($this->input('filter_params', '[]'), true);

        if (is_array($filterParamsDecoded)) {
            // Add contract types to the filter_params
            $documentType = DocumentTypeEnum::from($this->route('type'));
            $filterParamsDecoded['contract_types'] = $documentType
                ->contractTypes()
                ->map(fn ($contractType) => $contractType->value)
                ->toArray();

            $filterParamsDecoded['from_sublease'] = [null]; // Ensure that the automatically created contracts from subleases are not included
            $this->merge(['filter_params' => $filterParamsDecoded]);
        }

    }

    private function mapContractRenewFilter(?bool $filterValue, string $farmingYear)
    {
        $documentType = DocumentTypeEnum::from($this->route('type'));

        return [
            'year' => $farmingYear,
            'value' => $filterValue,
            'is_sublease' => DocumentTypeEnum::Subleases === $documentType,
        ];

    }

    private function getGroupedFilterParams(array $filterParams)
    {
        $contractTypes = $filterParams['contract_types'];
        unset($filterParams['contract_types']);

        $farmingYear = $filterParams['farming_year'] ?? $this->getCurrentFarmingYear();
        $filterParams['has_decreased_area_next_year'] = $this->mapContractRenewFilter($filterParams['has_decreased_area_next_year'] ?? null, $farmingYear);
        $filterParams['can_renew'] = $this->mapContractRenewFilter($filterParams['can_renew'] ?? null, $farmingYear);

        return ['filter_params' => [
            'groups' => [$filterParams],
            'contract_types' => $contractTypes,
            'farming_year' => $farmingYear,
        ]];
    }

    private function getCurrentFarmingYear()
    {
        $date = new DateTime();
        $year = (int) $date->format('Y');

        if ($date >= new DateTime($year . '-10-01')) {
            $year++;
        }

        return ($year - 1) . '/' . $year;
    }
}
