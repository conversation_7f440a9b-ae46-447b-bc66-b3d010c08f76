<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

abstract class BaseOwnerFilterPipe implements ContractPlotFilterPipe
{
    protected $filterValues;
    protected ?string $filterColumn;
    protected $isHeritor = false;
    protected $includeRepresentatives = false;

    public function handle(Builder $query, Closure $next)
    {
        $className = self::class;
        if (! is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues) || empty($this->filterColumn)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);
        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

            $query->whereIn(DB::raw($this->filterColumn), $filterValues)
                ->when(
                    $hasNullValue,
                    fn () => $query->orWhereNull(DB::raw($this->filterColumn))
                );
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;
        if (empty($this->filterColumn)) {
            throw new Exception("Filter column is not set for '{$className}'");
        }

        $this->joinRequiredTables($query);
        $query->selectRaw("{$this->filterColumn} AS value, {$this->filterColumn} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            // If the base table is 'layer_kvs', join 'su_plots_owners_rel' through 'su_contracts_plots_rel'
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query
                        ->leftJoin(
                            'su_plots_owners_rel',
                            fn ($join) => $join
                                ->on(
                                    fn ($subJoin) => $subJoin->on('su_plots_owners_rel.pc_rel_id', '=', 'su_contracts_plots_rel.id')
                                        ->orOn('su_plots_owners_rel.pc_rel_id', '=', 'su_sales_contracts_plots_rel.pc_rel_id')
                                )
                                ->when(
                                    $this->isHeritor,
                                    fn ($join) => $join->whereRaw('su_plots_owners_rel.is_heritor = TRUE')
                                )
                        )
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                ),

            // If the base table is 'su_contracts', join 'su_plots_owners_rel' through 'su_contracts_plots_rel'
            'su_contracts' =>  $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query
                        ->leftJoin(
                            'su_plots_owners_rel',
                            fn ($join) => $join
                                ->on('su_plots_owners_rel.pc_rel_id', '=', 'su_contracts_plots_rel.id')
                                ->when(
                                    $this->isHeritor,
                                    fn ($join) => $join->whereRaw('su_plots_owners_rel.is_heritor = TRUE')
                                )
                        )
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                ),

            // If the base table is 'su_sales_contracts', join 'su_plots_owners_rel' through 'su_sales_contracts_plots_rel'
            'su_sales_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_plots_owners_rel'),
                    fn () => $query
                        ->leftJoin(
                            'su_plots_owners_rel',
                            fn ($join) => $join
                                ->on('su_plots_owners_rel.pc_rel_id', '=', 'su_sales_contracts_plots_rel.pc_rel_id')
                                ->when(
                                    $this->isHeritor,
                                    fn ($join) => $join->whereRaw('su_plots_owners_rel.is_heritor = TRUE')
                                )
                        )
                )
                ->when(
                    ! $joinedTables->contains('su_owners'),
                    fn () => $query->leftJoin('su_owners', 'su_owners.id', '=', 'su_plots_owners_rel.owner_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by plot properties')
        };

        $query->when(
            $this->includeRepresentatives && ! $joinedTables->contains('su_owners_reps'),
            fn () => $query->leftJoin('su_owners_reps', 'su_owners_reps.id', '=', 'su_plots_owners_rel.rep_id')
        );
    }
}
