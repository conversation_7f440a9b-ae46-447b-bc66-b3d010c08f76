<?php

declare(strict_types=1);

namespace App\Http\Resources\Documents;

use App\Traits\Common\FarmingYearsTrait;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RenewContractsCollection extends ResourceCollection
{
    use FarmingYearsTrait;
    /**
     * The resource that this resource collects.
     */
    public $collects = RenewContractsResource::class;

    /**
     * Transform the resource collection into an array.
     */
    public function toArray($request): array
    {
        $reqParams = $request->all();
        $filterParams = json_decode($reqParams['filter_params'], true);
        [$farmingYearOffset, $farmingYear] = explode('/', $filterParams['farming_year']);
        $nextFarmingYear = self::getFarmingYearInfoFromDate($farmingYear . '-' . self::START_FARMING_YEAR);

        return [
            'columns' => $this->getColumns($filterParams['farming_year'], $nextFarmingYear['farming_year_short']),
            'rows' => $this->collection,
            'total' => $this->collection->first()['total_rows'] ?? null,
            'footer' => $this->getFooter($this->collection->first(), $filterParams['farming_year'], $nextFarmingYear['farming_year_short']),
        ];
    }

    private function getColumns($basePeriod, $nextPeriod): array
    {
        return [
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => false,
                'tooltip' => [],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
                'dataType' => 'text',
            ],
            [
                'name' => 'base_period',
                'title' => [$basePeriod],
                'sortable' => false,
                'tooltip' => [],
                'rowspan' => null,
                'colspan' => 2,
                'width' => '20%',
                'dataType' => 'text',
                'children' => [
                    [
                        'name' => 'document_area',
                        'title' => ['Document area(dka)'],
                        'sortable' => false,
                        'tooltip' => [],
                        'rowspan' => null,
                        'colspan' => null,
                        'width' => '50%',
                        'dataType' => 'text',
                    ],
                    [
                        'name' => 'contract_area',
                        'title' => ['Contracts area(dka)'],
                        'sortable' => false,
                        'tooltip' => [],
                        'rowspan' => null,
                        'colspan' => null,
                        'width' => '50%',
                        'dataType' => 'text',
                    ],
                ],
            ],
            [
                'name' => 'next_period',
                'title' => [$nextPeriod],
                'sortable' => false,
                'tooltip' => [],
                'rowspan' => null,
                'colspan' => 2,
                'width' => '20%',
                'dataType' => 'text',
                'children' => [
                    [
                        'name' => 'document_area',
                        'title' => ['Document area(dka)'],
                        'sortable' => false,
                        'tooltip' => [],
                        'rowspan' => null,
                        'colspan' => null,
                        'width' => '50%',
                        'dataType' => 'text',
                    ],
                    [
                        'name' => 'contract_area',
                        'title' => ['Contracts area(dka)'],
                        'sortable' => false,
                        'tooltip' => [],
                        'rowspan' => null,
                        'colspan' => null,
                        'width' => '50%',
                        'dataType' => 'text',
                    ],
                ],
            ],
        ];
    }

    private function getFooter($firstRow, $basePeriod, $nextPeriod)
    {
        if (! $firstRow) {
            return [
                [
                    'title' => 'Total area by document',
                    'value' => 0,
                    'year' => null,
                ],
                [
                    'title' => 'Total contract area',
                    'value' => 0,
                    'year' => $basePeriod,
                ],
                [
                    'title' => 'Total contract area',
                    'value' => 0,
                    'year' => $nextPeriod,
                ],
            ];
        }

        return [
            [
                'title' => 'Total area by document',
                'value' => $firstRow['total_document_area'],
                'year' => null,
            ],
            [
                'title' => 'Total contract area',
                'value' => $firstRow['total_contract_area_base_period'],
                'year' => $basePeriod,
            ],
            [
                'title' => 'Total contract area',
                'value' => $firstRow['total_contract_area_next_period'],
                'year' => $nextPeriod,
            ],
        ];
    }
}
