<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Illuminate\Database\Query\Builder;
use Exception;

class CnumFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $hasNullValue = in_array(null, $this->filterValues);
        $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

        $query->where(function ($query) use ($filterValues, $hasNullValue) {
            if (DocumentTypeEnum::tableNames()->doesntContain($query->from)) {
                // If the base table is not a contract table (e.g. layer_kvs), we need to filter by both contract tables - su_contracts and the su_sales_contracts using the OR operator
                // Note that both tables have the same column 'c_num'

                // Filter su_contracts table
                $query->where(function ($subQ) use ($filterValues, $hasNullValue) {
                    $subQ->whereIn('su_contracts.c_num', $filterValues)
                        ->when(
                            $hasNullValue,
                            fn () => $subQ->orWhereNull('su_contracts.c_num')
                        );
                });

                // Filter su_sales_contracts table
                $query->orWhere(function ($subQ) use ($filterValues, $hasNullValue) {
                    $subQ->whereIn('su_sales_contracts.c_num', $filterValues)
                        ->when(
                            $hasNullValue,
                            fn () => $subQ->orWhereNull('su_sales_contracts.c_num')
                        );
                });
            } else {
                // If the base table is a contract table, we need to filter by this specific contract table
                $query->whereIn("{$query->from}.c_num", $filterValues)
                    ->when(
                        $hasNullValue,
                        fn () => $query->orWhereNull("{$query->from}.c_num")
                    );
            }
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;
        $valueExpression = match($query->from) {
            'su_contracts',
            'su_sales_contracts' => "{$query->from}.c_num",
            'layer_kvs' => 'UNNEST(
                ARRAY_REMOVE(
                    ARRAY[su_contracts.c_num, su_sales_contracts.c_num],
                    NULL
                )
            )',
            default => throw new Exception("Invalid table for filter '{$className}' selection")

        };

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }
}
