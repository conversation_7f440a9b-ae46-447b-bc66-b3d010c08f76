<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

abstract class BaseOwnerOSZFilterPipe implements ContractPlotFilterPipe
{
    protected ?array $filterValues;
    protected ?string $filterColumn;

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues) || empty($this->filterColumn)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

            $query->whereIn(DB::raw($this->filterColumn), $filterValues)
                ->when(
                    $hasNullValue,
                    fn () => $query->orWhereNull(DB::raw($this->filterColumn))
                );
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        if (empty($this->filterColumn)) {
            throw new Exception('Filter column is not defined for BaseOwnerOSZFilterPipe');
        }

        $this->joinRequiredTables($query);
        $query->selectRaw("{$this->filterColumn} AS value, {$this->filterColumn} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            // If the base table is 'layer_kvs', join 'topic_layer_kvs_by_owner_name_label_items'
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'layer_kvs.gid')
                ),

            // If the base table is 'su_contracts', join 'topic_layer_kvs_by_owner_name_label_items' through 'su_contracts_plots_rel'
            'su_contracts' =>  $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.contract_id', '=', 'su_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'su_contracts_plots_rel.plot_id')
                ),

            // If the base table is 'su_sales_contracts', join 'topic_layer_kvs_by_owner_name_label_items' through 'su_sales_contracts_plots_rel'
            'su_sales_contracts' => $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('topic_layer_kvs_by_owner_name_label_items'),
                    fn () => $query->leftJoin('topic_layer_kvs_by_owner_name_label_items', 'topic_layer_kvs_by_owner_name_label_items.gid', '=', 'su_sales_contracts_plots_rel.plot_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by plot properties')
        };
    }
}
