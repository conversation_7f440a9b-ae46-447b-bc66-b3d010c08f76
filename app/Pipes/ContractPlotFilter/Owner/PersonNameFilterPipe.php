<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class PersonNameFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = "
            UNNEST(
                ARRAY_REMOVE(
                    ARRAY[
                        TRIM(su_owners.name) || ' ' || TRIM(su_owners.surname) || ' ' || TRIM(su_owners.lastname),
                        TRIM(su_owners_reps.rep_name) || ' ' || TRIM(su_owners_reps.rep_surname) || ' ' || TRIM(su_owners_reps.rep_lastname)
                    ],
                    NULL
                )
            )
        ";
        $this->includeRepresentatives = true;
    }

    public function handle(Builder $query, Closure $next)
    {
        $className = self::class;
        if (! is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->whereIn(DB::raw($this->filterColumn), $this->filterValues);

        return $next($query);
    }
}
