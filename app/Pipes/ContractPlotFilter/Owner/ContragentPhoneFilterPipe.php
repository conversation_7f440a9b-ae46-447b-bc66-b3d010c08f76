<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContragentPhoneFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->includeRepresentatives = true;
    }

    public function handle(Builder $query, Closure $next)
    {
        $className = self::class;
        if (! is_array($this->filterValues)) {
            throw new Exception("Filter values must be an array for '{$className}'");
        }

        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);
        $query->where(function ($query) {
            $hasNullValue = in_array(null, $this->filterValues);
            $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

            $query->whereIn('su_owners.phone', $filterValues)
                ->orWhereIn('su_owners.mobile', $filterValues)
                ->orWhereIn('su_owners_reps.rep_phone', $filterValues)
                ->when(
                    $hasNullValue,
                    fn () => $query->orWhereNull(DB::raw($this->filterColumn))
                );
        });

        return $next($query);
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $valueExpression = ' 
            UNNEST(
                    ARRAY_REMOVE(
                        ARRAY[
                            su_owners.phone,
                            su_owners.mobile,
                            su_owners_reps.rep_phone
                        ],
                        NULL
                    )
                )
        ';

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }
}
