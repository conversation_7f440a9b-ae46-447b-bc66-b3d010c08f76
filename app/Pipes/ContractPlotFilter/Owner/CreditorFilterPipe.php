<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Exception;
use Illuminate\Database\Query\Builder;

class CreditorFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = 'su_hypothecs_creditors.name';
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');
        $className = self::class;

        match($query->from) {
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_hypothecs_plots_rel'),
                    fn () => $query->leftJoin('su_hypothecs_plots_rel', 'su_hypothecs_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_hypothecs'),
                    fn () => $query->leftJoin('su_hypothecs', 'su_hypothecs.id', '=', 'su_hypothecs_plots_rel.hypothec_id')
                )
                ->when(
                    ! $joinedTables->contains('su_hypothecs_creditors'),
                    fn () => $query->leftJoin('su_hypothecs_creditors', 'su_hypothecs_creditors.id', '=', 'su_hypothecs.creditor_id')
                ),

            'su_hypothecs' =>  $query
                ->when(
                    ! $joinedTables->contains('su_hypothecs_creditors'),
                    fn () => $query->leftJoin('su_hypothecs_creditors', 'su_hypothecs_creditors.id', '=', 'su_hypothecs.creditor_id')
                ),

            default => throw new Exception("Invalid base table for filter {$className}")
        };

    }
}
