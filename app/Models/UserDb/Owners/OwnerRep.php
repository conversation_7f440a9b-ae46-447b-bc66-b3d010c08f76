<?php

declare(strict_types=1);

namespace App\Models\UserDb\Owners;

use App\Models\UserDb\Documents\Contracts\ContractContragent;
use App\Models\UserDb\Documents\Contracts\ContractFarmingContragent;
use App\Models\UserDb\Documents\Contracts\PlotOwner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Owner Representative Model
 * 
 * Represents the su_owners_reps table - owner representatives who can act on behalf of owners.
 */
class OwnerRep extends Model
{
    protected $table = 'su_owners_reps';

    protected $fillable = [
        'rep_name',
        'rep_surname',
        'rep_lastname',
        'rep_egn',
        'rep_lk',
        'rep_lk_izdavane',
        'rep_address',
        'owner_id',
        'rent_place',
        'iban',
        'rep_phone',
        'bic',
        'bank_name',
        'post_payment_fields',
    ];

    protected $casts = [
        'post_payment_fields' => 'array',
        'is_from_ao_migration' => 'boolean',
    ];

    /**
     * Get the owner that this representative belongs to (if any)
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Get the contract contragents for this representative
     */
    public function contractContragents(): HasMany
    {
        return $this->hasMany(ContractContragent::class, 'rep_id');
    }

    /**
     * Get the farming contragents for this representative
     */
    public function farmingContragents(): HasMany
    {
        return $this->hasMany(ContractFarmingContragent::class, 'rep_id');
    }

    /**
     * Get the plot owners for this representative
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'rep_id');
    }

    /**
     * Get full name for the representative
     */
    public function getFullNameAttribute(): string
    {
        $parts = array_filter([
            $this->rep_name,
            $this->rep_surname,
            $this->rep_lastname,
        ]);

        return implode(' ', $parts);
    }
}
