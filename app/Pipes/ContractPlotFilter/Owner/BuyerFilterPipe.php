<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

use Exception;
use Illuminate\Database\Query\Builder;

class BuyerFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = 'su_buyers.name';
    }

    public function joinRequiredTables(Builder $query): void
    {
        $className = self::class;
        $joinedTables = collect($query->joins)->pluck('table');


        match($query->from) {
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_buyers_rel'),
                    fn () => $query
                        ->leftJoin('su_sales_contracts_buyers_rel', 'su_sales_contracts_buyers_rel.sales_contract_id', '=', 'su_sales_contracts_plots_rel.sales_contract_id')
                )
                ->when(
                    ! $joinedTables->contains('su_buyers'),
                    fn () => $query->leftJoin('su_buyers', 'su_buyers.id', '=', 'su_sales_contracts_buyers_rel.buyer_id')
                ),

            'su_sales_contracts' =>  $query
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_buyers_rel'),
                    fn () => $query
                        ->leftJoin('su_sales_contracts_buyers_rel', 'su_sales_contracts_buyers_rel.sales_contract_id', '=', 'su_sales_contracts.id')
                )
                ->when(
                    ! $joinedTables->contains('su_buyers'),
                    fn () => $query->leftJoin('su_buyers', 'su_buyers.id', '=', 'su_sales_contracts_buyers_rel.buyer_id')
                ),

            default => throw new Exception("Invalid base table for filter {$className}")
        };
    }
}
