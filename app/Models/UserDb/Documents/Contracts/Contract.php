<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

// Relationship models are now in the same namespace
use App\Models\UserDb\Owners\Owner;
use App\Models\UserDb\Payments\Payment;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Contract extends AbstractContractModel
{
    protected $table = 'su_contracts';

    protected $primaryKey = 'id';

    // Define the fillable fields
    protected $fillable = [
        'c_num',
        'c_date',
        'nm_usage_rights',
        'sv_num',
        'sv_date',
        'start_date',
        'renta',
        'due_date',
        'renta_nat',
        'farming_id',
        'comment',
        'agg_type',
        'active',
        'parent_id',
        'is_annex',
        'renta_nat_type_id',
        'is_sublease',
        'original_due_date',
        'original_renta',
        'original_renta_nat',
        'original_renta_nat_type_id',
        'na_num',
        'tom',
        'delo',
        'court',
        'payday',
        'is_declaration_subleased',
        'from_sublease',
        'osz_date',
        'osz_num',
        'overall_renta',
        'is_closed_for_editing',
        'contract_price',
        'ao_c_num',
        'group',
        'farming_name'
    ];

    // Define the attributes that should be cast to native types
    protected $casts = [
        'c_date' => 'datetime',
        'sv_date' => 'datetime',
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'original_due_date' => 'datetime',
        'osz_date' => 'datetime',
        'is_sublease' => 'boolean',
        'is_annex' => 'boolean',
        'active' => 'boolean',
        'is_declaration_subleased' => 'boolean',
        'is_closed_for_editing' => 'boolean',
    ];

    /**
     * Get plot relationships - automatically loads from correct table based on contract type
     * For regular contracts: loads ContractPlot from su_contracts_plots_rel
     * For sublease contracts: loads SubleaseContractPlotArea from su_subleases_plots_area
     *
     * This method returns the appropriate relationship instance based on contract type
     */
    public function plots()
    {
        if ($this->is_sublease) {
            return $this->hasMany(SubleaseContractPlotArea::class, 'sublease_id');
        }

        return $this->hasMany(ContractPlot::class, 'contract_id');
    }

    public function getPlotsCollection()
    {
        // If sublease plots are loaded, return them (for subleases)
        if ($this->relationLoaded('subleasePlotAreas')) {
            return $this->subleasePlotAreas;
        }

        // If contract plots are loaded, return them (for regular contracts)
        if ($this->relationLoaded('contractPlots')) {
            return $this->contractPlots;
        }

        // Fallback: use the plots() relationship
        return $this->plots;
    }

    /**
     * Get regular contract plots
     */
    public function contractPlots()
    {
        return $this->hasMany(ContractPlot::class, 'contract_id');
    }

    /**
     * Get the parent contract (for annexes)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get the child contracts (annexes)
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get the contract from which this sublease originates
     */
    public function fromSublease(): BelongsTo
    {
        return $this->belongsTo(self::class, 'from_sublease');
    }

    /**
     * Get the contract group
     */
    public function contractGroup(): BelongsTo
    {
        return $this->belongsTo(ContractGroup::class, 'group');
    }

    /**
     * Get all plot owners for this contract through contract plots
     */
    public function plotOwners(): HasManyThrough
    {
        return $this->hasManyThrough(PlotOwner::class, ContractPlot::class, 'contract_id', 'pc_rel_id', 'id', 'id');
    }

    /**
     * Get all owners for this contract through contract plots and plot owners
     */
    public function owners(): HasManyThrough
    {
        return $this->hasManyThrough(Owner::class, PlotOwner::class, 'pc_rel_id', 'id', 'id', 'owner_id')
                    ->join('su_contracts_plots_rel', 'su_contracts_plots_rel.id', '=', 'su_plots_owners_rel.pc_rel_id')
                    ->where('su_contracts_plots_rel.contract_id', $this->id);
    }

    /**
     * Get the charged renta records
     */
    public function chargedRentas(): HasMany
    {
        return $this->hasMany(ContractChargedRenta::class, 'contract_id');
    }

    /**
     * Get the collections for this contract
     */
    public function collections(): HasMany
    {
        return $this->hasMany(ContractCollection::class, 'contract_id');
    }

    /**
     * Get the contract owners
     */
    public function contractOwners(): HasMany
    {
        return $this->hasMany(ContractOwner::class, 'contract_id');
    }

    /**
     * Get the contract contragents
     */
    public function contragents(): HasMany
    {
        return $this->hasMany(ContractContragent::class, 'contract_id');
    }

    /**
     * Get the farming contragents
     */
    public function farmingContragents(): HasMany
    {
        return $this->hasMany(ContractFarmingContragent::class, 'contract_id');
    }

    /**
     * Get the contract rents
     */
    public function rents(): HasMany
    {
        return $this->hasMany(ContractRent::class, 'contract_id');
    }

    /**
     * Get the payments for this contract
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'contract_id');
    }

    /**
     * Get the sales contract plots
     */
    public function salesContractPlots(): HasMany
    {
        return $this->hasMany(SalesContractPlot::class, 'contract_id');
    }

    /**
     * Get the sublease plot areas
     */
    public function subleasePlotAreas(): HasMany
    {
        return $this->hasMany(SubleaseContractPlotArea::class, 'sublease_id');
    }

    /**
     * Get the sublease contract plot relationships
     */
    public function subleaseContractPlots(): HasMany
    {
        return $this->hasMany(SubleaseContractPlot::class, 'sublease_id');
    }


    /**
     * Get the contract files
     */
    public function files(): HasMany
    {
        return $this->hasMany(ContractFile::class, 'contract_id');
    }

    /**
     * Scope for active contracts
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope for annexes
     */
    public function scopeAnnexes($query)
    {
        return $query->where('is_annex', true);
    }

    /**
     * Scope for subleases
     */
    public function scopeSubleases($query)
    {
        return $query->where('is_sublease', true);
    }

    /**
     * Scope for main contracts (not annexes)
     */
    public function scopeMainContracts($query)
    {
        return $query->where('is_annex', false);
    }
}
