<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContractRent extends Model
{
    protected $table = 'su_contracts_rents';

    public $timestamps = false;

    protected $fillable = [
        'contract_id',
        'renta_id',
        'renta_value',
    ];

    protected $casts = [
        'renta_value' => 'decimal:2',
    ];

    /**
     * Get the contract that owns this rent
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the renta type for this rent
     */
    public function rentaType(): BelongsTo
    {
        return $this->belongsTo(RentaType::class, 'renta_id');
    }
}
