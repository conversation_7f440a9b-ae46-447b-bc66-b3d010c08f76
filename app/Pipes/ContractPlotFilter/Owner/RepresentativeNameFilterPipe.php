<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Owner;

class RepresentativeNameFilterPipe extends BaseOwnerFilterPipe
{
    public function __construct(protected $filterValues)
    {
        $this->filterColumn = 'TRIM(su_owners_reps.rep_name) || \' \' || TRIM(su_owners_reps.rep_surname) || \' \' || TRIM(su_owners_reps.rep_lastname)';
        $this->includeRepresentatives = true;
    }
}
