<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Exception;
use Illuminate\Database\Query\Builder;

abstract class BaseContractFilterPipe implements ContractPlotFilterPipe
{
    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            // If the table in the from clause is 'su_contracts' or 'su_sales_contracts' we don't need to join any tables
            'su_contracts',
            'su_sales_contracts' => $query,

            // If the table in the from clause is 'layer_kvs' we need to join 'su_contracts' and 'su_sales_contracts' tables
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_sales_contracts_plots_rel', 'su_sales_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts'),
                    fn () => $query->leftJoin('su_sales_contracts', 'su_sales_contracts.id', '=', 'su_sales_contracts_plots_rel.sales_contract_id')
                ),

            default => throw new Exception('Error joining required tables for filtering by contract properties')
        };
    }
}
