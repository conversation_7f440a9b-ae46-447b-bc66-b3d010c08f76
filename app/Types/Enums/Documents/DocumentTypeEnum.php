<?php

declare(strict_types=1);

namespace App\Types\Enums\Documents;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Documents\Contracts\MortgageContract;
use App\Models\UserDb\Documents\Contracts\SalesContract;
use App\Services\Documents\Contracts\ContractsService;
use App\Services\Documents\Contracts\MortgageContractsService;
use App\Services\Documents\Contracts\SalesContractsService;
use App\Services\Documents\Contracts\SubleasesContractsService;
use Illuminate\Support\Collection;

enum DocumentTypeEnum: string
{
    case Contracts = 'contracts';
    case Ownership = 'ownership';
    case Subleases =  'subleases';
    case Sales =  'sales';
    case Mortgage =  'mortgage';

    public function contractTypes(): Collection
    {
        return match($this) {
            self::Contracts => collect([
                ContractTypeEnum::Rent,
                ContractTypeEnum::Lease,
                ContractTypeEnum::Agreement,
                ContractTypeEnum::JointProcessing,
            ]),
            self::Ownership => collect([
                ContractTypeEnum::Ownership,
            ]),
            self::Subleases => collect([
                ContractTypeEnum::Sublease,
            ]),
            self::Sales => collect([
                ContractTypeEnum::Sales,
            ]),
            self::Mortgage => collect([
                ContractTypeEnum::Mortgage,
            ]),
            default => collect(),
        };
    }

    public function tableName(): string
    {
        return match($this) {
            self::Contracts,
            self::Ownership,
            self::Subleases =>  (new Contract())->getTable(),
            self::Sales => (new SalesContract())->getTable(),
            self::Mortgage => (new MortgageContract())->getTable(),
        };
    }

    public function serviceClass(): string
    {
        return match($this) {
            self::Contracts,
            self::Ownership => ContractsService::class,
            self::Subleases => SubleasesContractsService::class,
            self::Sales => SalesContractsService::class,
            self::Mortgage => MortgageContractsService::class,
        };
    }

    public static function tableNames(): Collection
    {
        return collect(self::cases())->map(fn ($case) => $case->tableName());
    }

    public static function values(): Collection
    {
        return collect(self::cases())->map(fn ($case) => $case->value);
    }
}
