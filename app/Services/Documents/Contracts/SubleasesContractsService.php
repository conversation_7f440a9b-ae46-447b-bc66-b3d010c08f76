<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Types\DTOs\Common\ListDTO;
use App\Traits\Database\QueryHelper;
use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class SubleasesContractsService extends AbstractContractsService
{
    use QueryHelper;

    public function getContractsListFooter(array $contractIds): array
    {
        $contractsQuery = Contract::select('id', 'from_sublease')->when(
            ! empty($contractIds),
            fn ($q) => $q
                ->whereIn('su_contracts.id', $contractIds)
                ->where(
                    fn ($w) => $w
                        ->where('su_contracts.is_sublease', true)
                        ->orWhereNotNull('su_contracts.from_sublease')
                )
        )
            ->whereIn(DB::raw('get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date, FALSE)::TEXT'), [
                'Active',
            ]);


        $subleaseAreaQuery = DB::table('contracts AS c')
            ->selectRaw('
                c.id AS contract_id
                , SUM(COALESCE(sspa.contract_area, 0)) AS contract_area
            ')
            ->join('su_subleases_plots_contracts_rel AS sspcr', 'sspcr.sublease_id', '=', 'c.id')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.id', '=', 'sspcr.pc_rel_id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.id')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->groupBy(
                'c.id'
            );

        $areaByContractQuery = DB::table('contracts AS c')
            ->selectRaw('
                c.id AS contract_id
                , SUM(COALESCE(sspa.contract_area, 0)) AS contract_area
        ')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.from_sublease')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->unionAll($subleaseAreaQuery)
            ->groupBy(
                'c.id',
            );


        $footer = DB::table('area_by_contract')
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('area_by_contract', $areaByContractQuery)
            ->selectRaw('
                SUM(contract_area) as total_contract_area
            ')
            ->first();



        return (array) $footer;
    }

    public function getContracts(array $contractIds, array $allowedFarmingIds, ?int $page, ?int $limit, ?array $sort): array
    {
        $orderByExpr = $this->generateOrderByExpr($sort);

        $typeInt =  ContractTypeEnum::Sublease->value;
        $typeName = ContractTypeEnum::Sublease->name();

        // Administrative generated contracts
        $administrativeGeneratedContracts = Contract::selectRaw("
                su_contracts.id,
                su_contracts.c_num as name,
                TO_CHAR(su_contracts.c_date,'DD.MM.YYYY') as c_date,
                TO_CHAR(su_contracts.start_date,'DD.MM.YYYY') as start_date,
                TO_CHAR(su_contracts.due_date,'DD.MM.YYYY') as due_date,
                get_contract_status(su_contracts.id,su_contracts.active, su_contracts.start_date, su_contracts.due_date) as status,
                su_contracts.is_annex,
                su_contracts.is_sublease,
                case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
                su_contracts.from_sublease
        ")
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.from_sublease', $contractIds)
                    ->orWhere('su_contracts.id', $contractIds)
            );

        // Contracts
        $contractsQuery = Contract::selectRaw("
            ROW_NUMBER() OVER ({$orderByExpr}) as row_num,
            su_contracts.id,
            su_contracts.c_num,-- Contract number
            {$typeInt} as c_type,-- Contract type
            TO_CHAR(su_contracts.c_date,'DD.MM.YYYY') as c_date,
            TO_CHAR(su_contracts.start_date,'DD.MM.YYYY') as start_date,
            TO_CHAR(su_contracts.due_date,'DD.MM.YYYY') as due_date,
            su_contracts.active,
            get_contract_status(su_contracts.id,su_contracts.active, su_contracts.start_date, su_contracts.due_date) as status,
            '{$typeName}' as type,
            su_contracts.is_annex,
            su_contracts.is_sublease,
            case when max(cfc.id) is null then false else true end as  is_internal_sublease,
            su_contracts.from_sublease,
            case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
            su_contracts.farming_name,
        	su_contracts.osz_num, -- OSZ contract number 
        	TO_CHAR(su_contracts.osz_date,'DD.MM.YYYY') as osz_date, -- OSZ contract date
            su_contracts.sv_num, -- Registry Agency contract number
            TO_CHAR(su_contracts.sv_date,'DD.MM.YYYY') as sv_date,	-- Registry Agency date of contract
        	su_contracts.na_num,
        	su_contracts.tom,
        	su_contracts.total_contract_area,
        	su_contracts.delo,
        	su_contracts.court,
            su_contracts.comment,
            coalesce (
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'file_id',suf.id,
                        'file_name',suf.filename
                    )
                order by suf.id
                ) filter (where suf.id is not null),
            '[]'::json
            ) as files,
            coalesce(
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'id', COALESCE(agc.id, parent.id),
                        'name', COALESCE(agc.name, parent.c_num),
                        'c_date', COALESCE(agc.c_date, TO_CHAR(parent.c_date,'DD.MM.YYYY')),
                        'start_date', COALESCE(agc.start_date, TO_CHAR(parent.start_date,'DD.MM.YYYY')),
                        'due_date', COALESCE(agc.due_date, TO_CHAR(parent.due_date,'DD.MM.YYYY')),
                        'status', COALESCE(agc.status, get_contract_status(parent.id, parent.active, parent.start_date, parent.due_date)),
                        'c_type', {$typeInt},
                        'type', '{$typeName}',
                        'is_annex', COALESCE(agc.is_annex, parent.is_annex, false),
                        'is_sublease', COALESCE(agc.is_sublease, parent.is_sublease, false),
                        'is_from_sublease', COALESCE(agc.is_from_sublease, case when parent.from_sublease notnull then true else false end, false)
                    )
                ) filter (where agc.id is not null or parent.id is not null),
                '[]'::json
            ) as related_contracts
        ")
            ->leftJoin('su_contracts_files_rel AS scfr', 'scfr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_user_files AS suf', 'suf.id', '=', 'scfr.file_id')
            ->leftJoin('su_contracts_rents AS scr', 'scr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_renta_types AS srt', 'srt.id', '=', 'scr.renta_id')
            ->leftJoin('su_units_of_measure AS sum', 'sum.id', '=', 'srt.unit')
            ->leftJoin('administrative_generated_contracts AS agc', 'agc.from_sublease', '=', 'su_contracts.id')
            ->leftJoin(
                'su_contracts AS parent',
                fn ($q) => $q->on('parent.id', '=', 'su_contracts.from_sublease')
                    ->whereIn('parent.farming_id', $allowedFarmingIds)
            )
            ->leftJoin('su_contracts_farming_contragents AS cfc', 'cfc.contract_id', '=', 'su_contracts.id')
            ->when(
                empty($contractIds),
                fn ($q) => $q->where('su_contracts.is_sublease', true)
            )
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.id', $contractIds)
                    ->where(fn ($w) => $w->where('su_contracts.is_sublease', true)
                        ->orWhereNotNull('su_contracts.from_sublease'))
            )
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_contracts.id', 'parent.id');

        $mainDB = Config::get('database.connections.main');
        $connectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        // Contractors list
        $contractorsListQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id as contract_id,
                COALESCE(
                    NULLIF(uf.company, ''),
                    NULLIF(o.company_name, ''),
                    CONCAT_WS(' ', o.name, o.surname, o.lastname)
                ) AS name,
                coalesce(CONCAT_WS(' ', fsor.rep_name, fsor.rep_surname, fsor.rep_lastname), CONCAT_WS(' ', csor.rep_name, csor.rep_surname, csor.rep_lastname)) as rep_name
            ")
            ->leftJoin('su_contracts_farming_contragents AS cfc', 'cfc.contract_id', '=', 'c.id')
            ->leftJoin(DB::raw("dblink('{$connectionString}', 'SELECT id, company FROM su_users_farming') AS uf(id int, company text)"), 'uf.id', '=', 'cfc.farming_id')
            ->leftJoin('su_owners_reps AS fsor', 'fsor.id', '=', 'cfc.rep_id')
            ->leftJoin('su_contracts_contragents AS cc', 'cc.contract_id', '=', 'c.id')
            ->leftJoin('su_owners AS o', 'o.id', '=', 'cc.owner_id')
            ->leftJoin('su_owners_reps AS csor', 'csor.id', '=', 'cc.rep_id')
            ->orWhereNotNull('uf.id')
            ->orWhereNotNull('o.id')
            ->orWhereNotNull('fsor.id')
            ->orWhereNotNull('csor.id');

        // Contractors list in json format
        $contractorsListJsonQuery = DB::table('contractors_list AS ol')
            ->selectRaw('
                ol.contract_id,
                JSONB_AGG(ol) AS contractors_json
            ')
            ->groupBy('ol.contract_id');

        // SubleasesPlots list
        $SubleasesPlotsListUnionQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id AS contract_id,
                scpr.plot_id as id,
                lk.kad_ident,
                lk.virtual_ekatte_name AS ekatte_name,
                lk.virtual_ntp_title AS ntp_title,
                lk.document_area,
                TRIM(leading 'Категория ' from lk.virtual_category_title) AS category_title,
                coalesce(sspa.rent_area, 0) AS area_for_rent,
                lk.allowable_area,
                sspa.contract_area,
                sspa.comment,
                jsonb_agg(
                    distinct jsonb_build_object(
                        'id', pc.id,
                        'name', pc.c_num,
                        'contract_area', scpr.contract_area,
                        'c_date', TO_CHAR(pc.c_date,'DD.MM.YYYY'),
                        'start_date', TO_CHAR(pc.start_date,'DD.MM.YYYY'),
                        'due_date', TO_CHAR(pc.due_date,'DD.MM.YYYY'),
                        'status', get_contract_status(pc.id, pc.active, pc.start_date, pc.due_date),
                        'c_type', pc.nm_usage_rights,
                        'type', pc.virtual_contract_type
                    )
                ) AS contracts,
                JSONB_BUILD_ARRAY(
                    JSONB_BUILD_OBJECT('title', 'sublease', 'value', COALESCE(sspa.contract_area, 0)),
                    JSONB_BUILD_OBJECT('title', 'for rent', 'value', COALESCE(sspa.rent_area, 0)),
                    JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
                ) as plot_areas
            ")
            ->join('su_subleases_plots_contracts_rel AS sspcr', 'sspcr.sublease_id', '=', 'c.id')
            ->join('su_contracts_plots_rel AS scpr', 'scpr.id', '=', 'sspcr.pc_rel_id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'scpr.plot_id')
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.id')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->leftJoin('su_contracts AS pc', function (Builder $join) {
                $join->on('pc.id', '=', 'scpr.contract_id')
                    ->where('pc.active', '=', true);
            })
            ->groupBy(
                'c.id',
                'scpr.plot_id',
                'kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.virtual_category_title',
                'sspa.comment',
                'sspa.rent_area',
                'sspa.contract_area',
                'lk.allowable_area',
                'lk.document_area'
            );

        // Plots list
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
            c.id AS contract_id,
            scpr.plot_id as id,
            lk.kad_ident,
            lk.virtual_ekatte_name AS ekatte_name,
            lk.virtual_ntp_title AS ntp_title,
            lk.document_area,
            -- temporary trim the category title to get some space for the other columns in FE documents drawer      
            TRIM(leading 'Категория ' from lk.virtual_category_title) AS category_title     ,
            coalesce(sspa.rent_area, 0) AS area_for_rent,
            lk.allowable_area,
            sspa.contract_area,
            sspa.comment,
            '{}' as contracts,
            JSONB_BUILD_ARRAY(
                JSONB_BUILD_OBJECT('title', 'sublease', 'value', COALESCE(sspa.contract_area, 0)),
                JSONB_BUILD_OBJECT('title', 'for rent', 'value', COALESCE(sspa.rent_area, 0)),
                JSONB_BUILD_OBJECT('title', 'by document', 'value', COALESCE(lk.document_area, 0))
            ) as plot_areas
        ")
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'scpr.plot_id')
            ->leftJoin('su_contracts_plots_rel AS pscpr', 'pscpr.plot_id', '=', 'lk.gid')
            ->leftJoin('su_contracts AS pc', function (Builder $join) {
                $join->on('pc.id', '=', 'scpr.contract_id')
                    ->where('pc.active', '=', true);
            })
            ->join('su_subleases_plots_area AS sspa', function (Builder $join) {
                $join->on('sspa.sublease_id', '=', 'c.from_sublease')
                    ->on('sspa.plot_id', '=', 'scpr.plot_id');
            })
            ->unionAll($SubleasesPlotsListUnionQuery)
            ->groupBy(
                'c.id',
                'scpr.plot_id',
                'kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.virtual_category_title',
                'sspa.comment',
                'sspa.rent_area',
                'sspa.contract_area',
                'lk.allowable_area',
                'lk.document_area',
                'plot_areas'
            );

        // Plots list json
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id,
                JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json,
                ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names,
                ROUND(SUM(pl.contract_area)::numeric, 3) AS total_contract_area,
                ROUND(SUM(pl.area_for_rent)::numeric, 3) AS total_area_for_rent,
                ROUND(SUM(pl.document_area)::numeric, 3) AS total_document_area
            ')
            ->groupBy('pl.contract_id');

        $resultQuery = DB::table('contracts AS c')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names
                    ),
                    'contractors', oj.contractors_json,
                    'contractors_tree', null,
                    'plots', JSONB_BUILD_OBJECT(
                        'rows', plj.plots_json,
                        'footer', JSONB_BUILD_ARRAY(
                            JSONB_BUILD_OBJECT(
                                'title', 'sublease',
                                'value', COALESCE(plj.total_contract_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'for rent',
                                'value', COALESCE(plj.total_area_for_rent, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'by document',
                                'value', COALESCE(plj.total_document_area, 0)
                            )
                        )
                    )
                ) AS row
            ")
            ->leftJoin('contractors_list_json AS oj', 'oj.contract_id', '=', 'c.id')
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        return DB::table('result')
            ->withExpression('administrative_generated_contracts', $administrativeGeneratedContracts)
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('contractors_list', $contractorsListQuery)
            ->withExpression('contractors_list_json', $contractorsListJsonQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('result.row')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();
    }

    public function getContractsTotal(array $contractIds): int
    {
        return DB::table('su_contracts AS c')
            ->selectRaw('COUNT(DISTINCT c.id) AS total')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->where('c.is_sublease', true)
            ->first()->total ?? 0;
    }

    public function getPlotsForRenew(DocumentTypeEnum $documentType, array $filterParams, ?bool $canRenew = null, ?int $page = null, ?int $limit = null, ?array $sort = null): ListDTO
    {
        return new ListDTO([
            'rows' => [],
            'total' => 0,
        ]);
    }

    private function generateOrderByExpr(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_contracts.id',
            'c_num' => 'su_contracts.c_num',
            'start_date' => 'su_contracts.start_date',
            'farming_name' => 'su_contracts.farming_name',
            'total_contract_area' => 'su_contracts.total_contract_area',
            'type' => 'su_contracts.virtual_contract_type',
            'due_date' => 'su_contracts.due_date',
            'status' => 'su_contracts.virtual_contract_status',
            'comment' => 'su_contracts.comment',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };


        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return $this->generateOrderBySQL($sort);
    }
}
